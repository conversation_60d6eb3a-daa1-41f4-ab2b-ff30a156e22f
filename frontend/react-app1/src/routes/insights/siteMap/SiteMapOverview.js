import React, { useState, useEffect } from 'react';
import { Tabs, Spin } from 'antd';

const { TabPane } = Tabs;

const SiteMapOverview = ({ filterObject, searchQuery }) => {
    const [loading, setLoading] = useState(false);
    const [data, setData] = useState([]);

    // Mock data for sites
    const mockSiteData = [
        {
            site_id: 'SITE001',
            pincode: '110001',
            site_name: 'Delhi Central Site',
            address: 'Connaught Place, New Delhi',
            status: 'active',
            site_type: 'office',
            city: 'delhi',
        },
        {
            site_id: 'SITE002',
            pincode: '400001',
            site_name: 'Mumbai Fort Site',
            address: 'Fort, Mumbai',
            status: 'active',
            site_type: 'warehouse',
            city: 'mumbai',
        },
        {
            site_id: 'SITE003',
            pincode: '560001',
            site_name: 'Bangalore Central Site',
            address: 'MG Road, Bangalore',
            status: 'active',
            site_type: 'service_center',
            city: 'bangalore',
        },
        {
            site_id: 'SITE004',
            pincode: '600001',
            site_name: 'Chennai Central Site',
            address: 'George Town, Chennai',
            status: 'inactive',
            site_type: 'office',
            city: 'chennai',
        },
        {
            site_id: 'SITE005',
            pincode: '700001',
            site_name: 'Kolkata Central Site',
            address: 'BBD Bagh, Kolkata',
            status: 'active',
            site_type: 'warehouse',
            city: 'kolkata',
        },
        {
            site_id: 'SITE006',
            pincode: '500001',
            site_name: 'Hyderabad Central Site',
            address: 'Abids, Hyderabad',
            status: 'active',
            site_type: 'service_center',
            city: 'hyderabad',
        },
    ];

    useEffect(() => {
        fetchSiteData();
    }, [filterObject, searchQuery]);

    const fetchSiteData = () => {
        console.log('SiteMapOverview :: fetchSiteData :: called');
        setLoading(true);

        // Simulate API delay
        setTimeout(() => {
            let filteredData = mockSiteData;

            // Apply filters (QuickFilters returns arrays for multiple selections)
            if (filterObject.status && filterObject.status.length > 0) {
                filteredData = filteredData.filter((site) =>
                    filterObject.status.includes(site.status)
                );
            }
            if (filterObject.site_type && filterObject.site_type.length > 0) {
                filteredData = filteredData.filter((site) =>
                    filterObject.site_type.includes(site.site_type)
                );
            }
            if (filterObject.city && filterObject.city.length > 0) {
                filteredData = filteredData.filter((site) =>
                    filterObject.city.includes(site.city)
                );
            }

            // Apply search filter
            if (searchQuery) {
                filteredData = filteredData.filter(
                    (site) =>
                        site.site_id
                            .toLowerCase()
                            .includes(searchQuery.toLowerCase()) ||
                        site.pincode.includes(searchQuery) ||
                        site.site_name
                            .toLowerCase()
                            .includes(searchQuery.toLowerCase())
                );
            }

            console.log(
                'SiteMapOverview :: fetchSiteData :: filtered data:',
                filteredData
            );
            setData(filteredData);
            setLoading(false);
        }, 500);
    };

    if (loading) {
        return (
            <div
                className="gx-d-flex gx-justify-content-center gx-align-items-center"
                style={{ height: '400px' }}
            >
                <Spin size="large" />
                <div className="gx-ml-2">Loading site data...</div>
            </div>
        );
    }

    return (
        <div className="gx-p-1">
            <Tabs defaultActiveKey="marker" className="gx-text-capitalize">
                <TabPane
                    key="marker"
                    style={{ height: 'calc(100vh - 250px)' }}
                    tab={
                        <span>
                            <i className="icon icon-map-street-view"></i> MARKER
                        </span>
                    }
                >
                    <div className="gx-d-flex gx-justify-content-center gx-align-items-center gx-h-100">
                        <div className="gx-text-center">
                            <h3>Map View</h3>
                            <p>Showing {data.length} sites</p>
                            <div className="gx-mt-3">
                                {data.map((site) => (
                                    <div
                                        key={site.site_id}
                                        className="gx-mb-2 gx-p-2 gx-border"
                                    >
                                        <strong>Pincode: {site.pincode}</strong>
                                        <br />
                                        Site ID: {site.site_id}
                                        <br />
                                        <small>
                                            {site.site_name} - {site.address}
                                        </small>
                                    </div>
                                ))}
                            </div>
                        </div>
                    </div>
                </TabPane>

                <TabPane
                    key="heat_map"
                    style={{ height: 'calc(100vh - 250px)' }}
                    tab={
                        <span>
                            <i className="icon icon-map-clustering gx-mr-1"></i>{' '}
                            HEAT MAP
                        </span>
                    }
                >
                    <div className="gx-d-flex gx-justify-content-center gx-align-items-center gx-h-100">
                        <div className="gx-text-center">
                            <h3>Heat Map View</h3>
                            <p>
                                Heat map visualization will be implemented here
                            </p>
                            <p>Showing {data.length} sites</p>
                        </div>
                    </div>
                </TabPane>
            </Tabs>
        </div>
    );
};

export default SiteMapOverview;
